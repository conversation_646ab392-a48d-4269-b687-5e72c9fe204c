services:
  postgres:
    image: postgres:15
    restart: unless-stopped
    env_file:
      - .postgresql.env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -h localhost -U n8n -d n8n']
      interval: 5s
      timeout: 5s
      retries: 10

  workflow-importer:
    build: .
    restart: "no"
    env_file:
      - .n8n.env
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./scripts:/scripts
      - ./workflows:/workflows
    depends_on:
      postgres:
        condition: service_healthy
    entrypoint: ["sh", "-c"]
    command: ["/scripts/import-workflows.sh"]

  n8n:
    build: .
    restart: unless-stopped
    env_file:
      - .n8n.env
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
    depends_on:
      postgres:
        condition: service_healthy
      workflow-importer:
        condition: service_completed_successfully

volumes:
  postgres_data:
  n8n_data: