echo "🔁 Checking existing workflows in n8n..."

# Wait for n8n to be ready
echo "😴 Sleeping for 15 seconds while waiting for n8n..."
sleep 15

# Get existing workflow IDs from n8n instance
EXISTING_IDS=$(n8n list workflow --raw-output --no-color 2>/dev/null | jq -r '.[].id // empty' 2>/dev/null)

echo "📋 Existing workflow IDs:"
echo "$EXISTING_IDS"

# Loop over local workflow files
for file in /workflows/*.json; do
  # Extract workflow ID from JSON (handle potential parsing errors)
  LOCAL_ID=$(jq -r '.id // empty' "$file" 2>/dev/null)

  # Skip if null or empty
  if [ -z "$LOCAL_ID" ] || [ "$LOCAL_ID" = "null" ]; then
    echo "⚠️  No ID found in $file, uploading as new..."
    n8n import:workflow --input="$file" --separate
    continue
  fi

  # Check if ID already exists
  if echo "$EXISTING_IDS" | grep -q "^${LOCAL_ID}$"; then
    echo "⏭️  Workflow $LOCAL_ID already exists, skipping $file"
  else
    echo "📥 Importing new workflow $file with ID $LOCAL_ID"
    n8n import:workflow --input="$file" --separate
  fi
done

echo "✅ Workflow import check complete"
