echo "🔁 Checking existing workflows in n8n..."

# Wait for n8n to be ready
echo "😴 Sleeping for 15 seconds while waiting for n8n..."
sleep 15

# Get existing workflow IDs from n8n instance
echo "🔍 Checking n8n list:workflow command..."
RAW_OUTPUT=$(n8n list:workflow 2>&1)
echo "Raw n8n output: $RAW_OUTPUT"

if [ $? -eq 0 ] && [ -n "$RAW_OUTPUT" ]; then
  EXISTING_IDS=$(echo "$RAW_OUTPUT" | jq -r '.[].id // empty' 2>/dev/null)
  echo "📋 Existing workflow IDs:"
  echo "$EXISTING_IDS"
else
  echo "⚠️  n8n list:workflow failed or returned empty"
  EXISTING_IDS=""
fi

# Loop over local workflow files
for file in /workflows/*.json; do
  # Extract workflow ID from JSON (handle potential parsing errors)
  LOCAL_ID=$(jq -r '.id // empty' "$file" 2>/dev/null)

  # Skip if null or empty
  if [ -z "$LOCAL_ID" ] || [ "$LOCAL_ID" = "null" ]; then
    echo "⚠️  No ID found in $file, uploading as new..."
    n8n import:workflow --input="$file" --separate
    continue
  fi

  # Check if ID already exists
  echo "🔍 Checking if LOCAL_ID '$LOCAL_ID' exists in EXISTING_IDS:"
  echo "EXISTING_IDS: '$EXISTING_IDS'"

  if echo "$EXISTING_IDS" | grep -Fxq "$LOCAL_ID"; then
    echo "⏭️  Workflow $LOCAL_ID already exists, skipping $file"
  else
    echo "📥 Importing new workflow $file with ID $LOCAL_ID"
    n8n import:workflow --input="$file" --separate
  fi
done

echo "✅ Workflow import check complete"
