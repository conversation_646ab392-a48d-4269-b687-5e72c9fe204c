echo "🔁 Checking existing workflows in n8n..."

# Wait for n8n to be ready
sleep 10

# Get existing workflow IDs from n8n instance
EXISTING_IDS=$(n8n list:workflow --raw-output --no-color | jq -r '.[].id')

echo "📋 Existing workflow IDs:"
echo "$EXISTING_IDS"

# Loop over local workflow files
for file in /workflows/*.json; do
  # Extract workflow ID from JSON
  LOCAL_ID=$(jq -r '.id' "$file")

  # Skip if null or empty
  if [ -z "$LOCAL_ID" ] || [ "$LOCAL_ID" = "null" ]; then
    echo "⚠️  No ID found in $file, uploading as new..."
    n8n import:workflow --input="$file" --separate
    continue
  fi

  # Check if ID already exists
  if echo "$EXISTING_IDS" | grep -q "^${LOCAL_ID}$"; then
    echo "⏭️  Workflow $LOCAL_ID already exists, skipping $file"
  else
    echo "📥 Importing new workflow $file with ID $LOCAL_ID"
    n8n import:workflow --input="$file" --separate
  fi
done

echo "✅ Workflow import check complete"
